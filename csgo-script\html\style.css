* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
    color: white;
    overflow: hidden;
}

#container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
}

.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hidden {
    display: none !important;
}

/* Loading Screen */
.loading-content {
    text-align: center;
    animation: fadeIn 0.5s ease-in;
}

.case-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: bounce 2s infinite;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

/* Case Opening Animation */
.case-opening-container {
    width: 90%;
    max-width: 1200px;
    text-align: center;
}

.case-header h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.reel-container {
    position: relative;
    width: 100%;
    height: 200px;
    background: linear-gradient(90deg, 
        rgba(0, 0, 0, 0.8) 0%, 
        rgba(0, 0, 0, 0.2) 20%, 
        rgba(0, 0, 0, 0.1) 50%, 
        rgba(0, 0, 0, 0.2) 80%, 
        rgba(0, 0, 0, 0.8) 100%);
    border: 2px solid #444;
    border-radius: 10px;
    overflow: hidden;
    margin: 2rem 0;
}

.reel-window {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.selector-line {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, 
        transparent 0%, 
        #ff6b6b 20%, 
        #ff6b6b 80%, 
        transparent 100%);
    z-index: 10;
    box-shadow: 0 0 20px #ff6b6b;
}

.item-reel {
    display: flex;
    height: 100%;
    align-items: center;
    transition: transform cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.reel-item {
    min-width: 150px;
    height: 140px;
    margin: 0 10px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.reel-item:hover {
    transform: scale(1.05);
}

.item-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.item-name {
    font-size: 0.9rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 0.25rem;
}

.item-rarity {
    font-size: 0.7rem;
    text-transform: uppercase;
    opacity: 0.8;
}

/* Result Screen */
.result-content {
    text-align: center;
    animation: slideUp 0.8s ease-out;
}

.result-header h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.won-item-container {
    margin: 2rem 0;
}

.won-item {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border: 3px solid;
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    animation: glow 2s ease-in-out infinite alternate;
}

.won-item .item-icon {
    font-size: 4rem;
    margin-right: 1.5rem;
}

.item-info h3 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.item-info p {
    font-size: 1.2rem;
    text-transform: uppercase;
    font-weight: bold;
}

.close-button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 2rem;
}

.close-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(50px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(255, 255, 255, 0.2); }
    to { box-shadow: 0 0 30px rgba(255, 255, 255, 0.4); }
}

/* Rarity-specific styles */
.rarity-common {
    border-color: #b0c3d9;
    box-shadow: 0 0 15px rgba(176, 195, 217, 0.3);
}
.rarity-uncommon {
    border-color: #5e98d9;
    box-shadow: 0 0 15px rgba(94, 152, 217, 0.3);
}
.rarity-rare {
    border-color: #4b69ff;
    box-shadow: 0 0 15px rgba(75, 105, 255, 0.3);
}
.rarity-epic {
    border-color: #8847ff;
    box-shadow: 0 0 15px rgba(136, 71, 255, 0.3);
}
.rarity-legendary {
    border-color: #d32ce6;
    box-shadow: 0 0 15px rgba(211, 44, 230, 0.3);
}
.rarity-mythical {
    border-color: #eb4b4b;
    box-shadow: 0 0 15px rgba(235, 75, 75, 0.3);
}
