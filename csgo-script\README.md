# CS:GO Case Opening System for ESX FiveM

A complete CS:GO-style case opening system for ESX FiveM servers with realistic spinning animations and rarity-based rewards.

## Features

- 🎮 **Authentic CS:GO Animation**: Realistic spinning reel animation with deceleration
- 🎨 **Rarity System**: 6 different rarity levels with color-coded items
- 🔊 **Sound Effects**: Built-in sound effects for case opening
- 📱 **Responsive UI**: Modern, responsive interface that works on all screen sizes
- ⚙️ **Configurable**: Easy to configure cases, items, and probabilities
- 🎯 **ESX Integration**: Full integration with ESX framework
- 📊 **Admin Commands**: Admin commands to give cases to players

## Installation

### 1. Database Setup
Run the SQL commands in `csgo_cases.sql` to add the required items to your database:
```sql
-- Execute the contents of csgo_cases.sql in your database
```

### 2. Resource Installation
1. Copy the `csgo_cases` folder to your server's `resources` directory
2. Add `ensure csgo_cases` to your `server.cfg`
3. Restart your server

### 3. Dependencies
Make sure you have these resources installed:
- `es_extended` (ESX Framework)
- `oxmysql` (Database connector)

## Configuration

Edit `config.lua` to customize:

### Cases and Items
```lua
Config.Cases = {
    ['weapon_case'] = {
        label = 'Weapon Case',
        price = 2500,
        items = {
            {item = 'weapon_pistol', label = 'Pistol', rarity = 'common', chance = 30},
            -- Add more items...
        }
    }
}
```

### Rarity Colors
```lua
Config.RarityColors = {
    ['common'] = '#b0c3d9',
    ['uncommon'] = '#5e98d9',
    -- Customize colors...
}
```

### Animation Settings
```lua
Config.Animation = {
    duration = 5000, -- Animation duration in milliseconds
    itemsToShow = 15, -- Number of items in the reel
    slowdownStart = 0.7 -- When to start slowing down (0.7 = 70%)
}
```

## Usage

### For Players
1. **Obtain a case**: Get cases from admins or through gameplay
2. **Open inventory**: Use your inventory system to find the case
3. **Use the case**: Click/use the case item to start opening
4. **Confirm opening**: Choose "Yes" to confirm case opening
5. **Watch animation**: Enjoy the CS:GO-style spinning animation
6. **Receive reward**: Get your random item based on rarity chances

### For Admins
Give cases to players using the admin command:
```
/givecase [player_id] [case_type] [amount]
```

Examples:
```
/givecase 1 weapon_case 5
/givecase 2 skin_case 1
```

### Testing
Use the test command (for development):
```
/testcase weapon_case
```

## Rarity System

The system includes 6 rarity levels:

| Rarity | Color | Description |
|--------|-------|-------------|
| Common | Light Blue | Most common items |
| Uncommon | Blue | Slightly rare items |
| Rare | Dark Blue | Rare items |
| Epic | Purple | Very rare items |
| Legendary | Pink | Extremely rare items |
| Mythical | Red | Ultra rare items |

## Customization

### Adding New Cases
1. Add items to your database
2. Configure the case in `config.lua`
3. Register the case as a usable item

### Modifying Animation
- Edit `html/style.css` for visual changes
- Modify `html/script.js` for animation behavior
- Adjust `Config.Animation` settings

### Custom Sounds
Add sound files to `html/sounds/` and reference them in the JavaScript.

## File Structure
```
csgo_cases/
├── fxmanifest.lua          # Resource manifest
├── config.lua              # Configuration file
├── server/
│   └── main.lua            # Server-side logic
├── client/
│   └── main.lua            # Client-side logic
├── html/
│   ├── index.html          # UI structure
│   ├── style.css           # UI styling
│   ├── script.js           # UI logic
│   └── sounds/             # Sound effects (optional)
├── csgo_cases.sql          # Database setup
└── README.md               # This file
```

## Troubleshooting

### Common Issues

1. **Cases not working**: Make sure items are added to database
2. **UI not showing**: Check browser console for JavaScript errors
3. **Animation stuttering**: Reduce `itemsToShow` in config
4. **Items not received**: Verify ESX inventory integration

### Debug Commands
- `/testcase [case_type]` - Test case opening without consuming items

## Support

For support and updates, check the resource documentation or contact the developer.

## License

This resource is provided as-is for FiveM servers. Modify as needed for your server.
