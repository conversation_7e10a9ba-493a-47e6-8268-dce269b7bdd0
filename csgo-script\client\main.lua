ESX = exports["es_extended"]:getSharedObject()

local isUIOpen = false
local currentCase = nil

-- Open case event
RegisterNetEvent('csgo_cases:openCase')
AddEventHandler('csgo_cases:openCase', function(caseType)
    if isUIOpen then return end
    
    currentCase = caseType
    local caseConfig = Config.Cases[caseType]
    
    if not caseConfig then return end
    
    -- Show confirmation dialog
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'confirm_case_opening', {
        title = 'Open ' .. caseConfig.label,
        align = 'top-left',
        elements = {
            {label = 'Yes, open case', value = 'yes'},
            {label = 'Cancel', value = 'no'}
        }
    }, function(data, menu)
        if data.current.value == 'yes' then
            menu.close()
            openCaseAnimation(caseType)
        else
            menu.close()
        end
    end, function(data, menu)
        menu.close()
    end)
end)

-- Show case opening animation
function openCaseAnimation(caseType)
    isUIOpen = true
    
    -- Disable player controls during animation
    SetNuiFocus(true, true)
    
    -- Send case opening request to server
    TriggerServerEvent('csgo_cases:processCase', caseType)
    
    -- Show loading screen
    SendNUIMessage({
        type = 'showLoading',
        caseType = caseType,
        caseLabel = Config.Cases[caseType].label
    })
end

-- Handle case opening result
RegisterNetEvent('csgo_cases:showResult')
AddEventHandler('csgo_cases:showResult', function(data)
    -- Start the spinning animation
    SendNUIMessage({
        type = 'startAnimation',
        wonItem = data.wonItem,
        animationItems = data.animationItems,
        caseType = data.caseType,
        rarityColors = Config.RarityColors,
        animationDuration = Config.Animation.duration
    })
end)

-- NUI Callbacks
RegisterNUICallback('closeUI', function(data, cb)
    SetNuiFocus(false, false)
    isUIOpen = false
    currentCase = nil
    cb('ok')
end)

RegisterNUICallback('animationComplete', function(data, cb)
    -- Show notification about won item
    ESX.ShowNotification('You won: ' .. data.wonItem.label .. '!', 'success')
    
    -- Play sound effect
    PlaySoundFrontend(-1, "CHECKPOINT_PERFECT", "HUD_MINI_GAME_SOUNDSET", 1)
    
    cb('ok')
end)

-- Disable controls during case opening
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        if isUIOpen then
            -- Disable all controls
            DisableAllControlActions(0)
            DisableAllControlActions(1)
            DisableAllControlActions(2)
            
            -- Allow mouse movement for UI
            EnableControlAction(0, 1, true) -- LookLeftRight
            EnableControlAction(0, 2, true) -- LookUpDown
            EnableControlAction(0, 142, true) -- MeleeAttackAlternate (mouse wheel)
        end
    end
end)

-- Debug command to test case opening
RegisterCommand('testcase', function(source, args)
    local caseType = args[1] or 'weapon_case'
    if Config.Cases[caseType] then
        TriggerEvent('csgo_cases:openCase', caseType)
    else
        ESX.ShowNotification('Invalid case type!', 'error')
    end
end)

-- Add items to ESX inventory (if using esx_inventory)
Citizen.CreateThread(function()
    while ESX == nil do
        Citizen.Wait(100)
    end
    
    -- Register case items
    for caseType, caseData in pairs(Config.Cases) do
        -- This would typically be done in the database, but for testing:
        -- You'll need to add these items to your items table in the database
    end
end)
