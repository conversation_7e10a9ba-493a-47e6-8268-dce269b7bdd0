-- CS:GO Cases Database Setup
-- Add these items to your ESX items table

INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES
-- Cases
('weapon_case', 'Weapon Case', 1, 0, 1),
('skin_case', 'Skin Case', 1, 0, 1),

-- Weapons (adjust these based on your server's weapon items)
('weapon_pistol', 'Pistol', 2, 0, 1),
('weapon_smg', 'SMG', 3, 0, 1),
('weapon_assaultrifle', 'Assault Rifle', 4, 0, 1),
('weapon_sniperrifle', 'Sniper Rifle', 5, 0, 1),
('weapon_rpg', 'RPG', 10, 1, 1),
('weapon_minigun', 'Minigun', 15, 1, 1),

-- Skins/Cosmetics
('skin_common', 'Common Skin', 0, 0, 1),
('skin_uncommon', 'Uncommon Skin', 0, 0, 1),
('skin_rare', 'Rare Skin', 0, 0, 1),
('skin_epic', 'Epic Skin', 0, 1, 1),
('skin_legendary', 'Legendary Skin', 0, 1, 1);

-- Optional: Create a table to track case openings for statistics
CREATE TABLE IF NOT EXISTS `csgo_case_openings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(60) NOT NULL,
  `case_type` varchar(50) NOT NULL,
  `won_item` varchar(50) NOT NULL,
  `won_rarity` varchar(20) NOT NULL,
  `opened_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `identifier` (`identifier`),
  KEY `case_type` (`case_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
