let isAnimating = false;
let currentData = null;

// Listen for messages from the game
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.type) {
        case 'showLoading':
            showLoadingScreen(data);
            break;
        case 'startAnimation':
            startCaseAnimation(data);
            break;
    }
});

function showLoadingScreen(data) {
    const container = document.getElementById('container');
    const loadingScreen = document.getElementById('loading-screen');
    const caseTitle = document.getElementById('case-title');
    
    container.classList.remove('hidden');
    loadingScreen.classList.remove('hidden');
    caseTitle.textContent = `Opening ${data.caseLabel}...`;
}

function startCaseAnimation(data) {
    currentData = data;
    isAnimating = true;
    
    // Hide loading screen and show animation screen
    document.getElementById('loading-screen').classList.add('hidden');
    document.getElementById('animation-screen').classList.remove('hidden');
    
    // Set case title
    document.getElementById('animation-case-title').textContent = `Opening ${data.caseType.replace('_', ' ').toUpperCase()}`;
    
    // Create reel items
    createReelItems(data.animationItems, data.rarityColors);
    
    // Start the spinning animation
    setTimeout(() => {
        animateReel(data.wonItem, data.animationDuration);
    }, 500);
}

function createReelItems(items, rarityColors) {
    const reel = document.getElementById('item-reel');
    reel.innerHTML = '';
    
    // Add extra items at the beginning for smooth animation
    const extraItems = 5;
    const allItems = [];
    
    // Add some random items at the start
    for (let i = 0; i < extraItems; i++) {
        allItems.push(items[Math.floor(Math.random() * items.length)]);
    }
    
    // Add the main items
    allItems.push(...items);
    
    // Add some random items at the end
    for (let i = 0; i < extraItems; i++) {
        allItems.push(items[Math.floor(Math.random() * items.length)]);
    }
    
    allItems.forEach((item, index) => {
        const itemElement = document.createElement('div');
        itemElement.className = `reel-item rarity-${item.rarity}`;
        
        // Set border color based on rarity
        if (rarityColors[item.rarity]) {
            itemElement.style.borderColor = rarityColors[item.rarity];
        }
        
        itemElement.innerHTML = `
            <div class="item-icon">${getItemIcon(item.item)}</div>
            <div class="item-name">${item.label}</div>
            <div class="item-rarity">${item.rarity}</div>
        `;
        
        reel.appendChild(itemElement);
    });
}

function getItemIcon(itemName) {
    // Map item names to emojis/icons
    const iconMap = {
        'weapon_pistol': '🔫',
        'weapon_smg': '🔫',
        'weapon_assaultrifle': '🔫',
        'weapon_sniperrifle': '🎯',
        'weapon_rpg': '🚀',
        'weapon_minigun': '💥',
        'skin_common': '🎨',
        'skin_uncommon': '🎨',
        'skin_rare': '🎨',
        'skin_epic': '🎨',
        'skin_legendary': '🎨'
    };
    
    return iconMap[itemName] || '🎁';
}

function animateReel(wonItem, duration) {
    const reel = document.getElementById('item-reel');
    const reelWindow = document.querySelector('.reel-window');
    const items = reel.children;
    
    if (items.length === 0) return;
    
    const itemWidth = 170; // 150px width + 20px margin
    const windowWidth = reelWindow.offsetWidth;
    const centerOffset = windowWidth / 2 - itemWidth / 2;
    
    // Find the won item position (should be around 80% through the items)
    let wonItemIndex = -1;
    for (let i = 0; i < items.length; i++) {
        const itemName = items[i].querySelector('.item-name').textContent;
        if (itemName === wonItem.label && i > items.length * 0.7) {
            wonItemIndex = i;
            break;
        }
    }
    
    if (wonItemIndex === -1) {
        wonItemIndex = Math.floor(items.length * 0.8);
    }
    
    // Calculate final position to center the won item
    const finalPosition = -(wonItemIndex * itemWidth - centerOffset);
    
    // Start with fast spinning
    let currentPosition = 0;
    const startTime = Date.now();
    
    function animate() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function for realistic deceleration
        const easeOut = 1 - Math.pow(1 - progress, 3);
        
        // Calculate current position
        const totalDistance = finalPosition - currentPosition;
        currentPosition = finalPosition - totalDistance * (1 - easeOut);
        
        reel.style.transform = `translateX(${currentPosition}px)`;
        
        if (progress < 1) {
            requestAnimationFrame(animate);
        } else {
            // Animation complete
            setTimeout(() => {
                showResult(wonItem);
            }, 1000);
        }
    }
    
    // Add some initial momentum
    reel.style.transform = `translateX(-${itemWidth * 3}px)`;
    
    setTimeout(() => {
        animate();
    }, 100);
}

function showResult(wonItem) {
    // Hide animation screen and show result screen
    document.getElementById('animation-screen').classList.add('hidden');
    document.getElementById('result-screen').classList.remove('hidden');
    
    // Set won item details
    document.getElementById('won-item-name').textContent = wonItem.label;
    document.getElementById('won-item-rarity').textContent = wonItem.rarity.toUpperCase();
    
    const wonItemElement = document.getElementById('won-item');
    wonItemElement.className = `won-item rarity-${wonItem.rarity}`;
    
    // Set rarity color
    if (currentData.rarityColors[wonItem.rarity]) {
        wonItemElement.style.borderColor = currentData.rarityColors[wonItem.rarity];
    }
    
    // Update icon
    const iconElement = wonItemElement.querySelector('.item-icon');
    iconElement.textContent = getItemIcon(wonItem.item);
    
    // Notify the game that animation is complete
    fetch(`https://${GetParentResourceName()}/animationComplete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            wonItem: wonItem
        })
    });
    
    isAnimating = false;
}

// Close button event
document.getElementById('close-btn').addEventListener('click', function() {
    closeUI();
});

function closeUI() {
    const container = document.getElementById('container');
    container.classList.add('hidden');
    
    // Reset all screens
    document.getElementById('loading-screen').classList.add('hidden');
    document.getElementById('animation-screen').classList.add('hidden');
    document.getElementById('result-screen').classList.add('hidden');
    
    // Notify the game to close UI
    fetch(`https://${GetParentResourceName()}/closeUI`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
    
    isAnimating = false;
    currentData = null;
}

// Close UI on ESC key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && !isAnimating) {
        closeUI();
    }
});

// Prevent context menu
document.addEventListener('contextmenu', function(event) {
    event.preventDefault();
});
