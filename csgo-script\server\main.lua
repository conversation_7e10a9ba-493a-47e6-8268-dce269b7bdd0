ESX = exports["es_extended"]:getSharedObject()

-- Register usable items for cases
for caseType, caseData in pairs(Config.Cases) do
    ESX.RegisterUsableItem(caseType, function(source)
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer then
            TriggerClientEvent('csgo_cases:openCase', source, caseType)
        end
    end)
end

-- Handle case opening
RegisterServerEvent('csgo_cases:processCase')
AddEventHandler('csgo_cases:processCase', function(caseType)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then return end
    
    local caseConfig = Config.Cases[caseType]
    if not caseConfig then return end
    
    -- Check if player has the case
    local caseItem = xPlayer.getInventoryItem(caseType)
    if not caseItem or caseItem.count < 1 then
        TriggerClientEvent('esx:showNotification', source, 'You don\'t have this case!')
        return
    end
    
    -- Remove the case from inventory
    xPlayer.removeInventoryItem(caseType, 1)
    
    -- Generate random item based on chances
    local wonItem = getRandomItem(caseConfig.items)
    
    if wonItem then
        -- Add the won item to inventory
        xPlayer.addInventoryItem(wonItem.item, 1)
        
        -- Generate animation items (including the won item)
        local animationItems = generateAnimationItems(caseConfig.items, wonItem)
        
        -- Send result to client
        TriggerClientEvent('csgo_cases:showResult', source, {
            wonItem = wonItem,
            animationItems = animationItems,
            caseType = caseType
        })
        
        -- Log the case opening
        print(string.format('[CSGO Cases] Player %s opened %s and won %s', 
            xPlayer.getName(), caseType, wonItem.label))
    end
end)

-- Function to get random item based on chances
function getRandomItem(items)
    local totalChance = 0
    for _, item in pairs(items) do
        totalChance = totalChance + item.chance
    end
    
    local random = math.random(1, totalChance)
    local currentChance = 0
    
    for _, item in pairs(items) do
        currentChance = currentChance + item.chance
        if random <= currentChance then
            return item
        end
    end
    
    return items[1] -- Fallback
end

-- Function to generate items for animation
function generateAnimationItems(items, wonItem)
    local animationItems = {}
    local itemsToGenerate = Config.Animation.itemsToShow
    
    -- Add random items for animation
    for i = 1, itemsToGenerate - 1 do
        local randomItem = getRandomItem(items)
        table.insert(animationItems, randomItem)
    end
    
    -- Insert the won item at a specific position (usually near the end)
    local winPosition = math.floor(itemsToGenerate * 0.8) -- 80% through the items
    table.insert(animationItems, winPosition, wonItem)
    
    return animationItems
end

-- Command to give cases (admin only)
ESX.RegisterCommand('givecase', 'admin', function(xPlayer, args, showError)
    local targetId = tonumber(args.target)
    local caseType = args.case
    local amount = tonumber(args.amount) or 1
    
    if not targetId or not caseType then
        TriggerClientEvent('chat:addMessage', xPlayer.source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"System", "Usage: /givecase [player_id] [case_type] [amount]"}
        })
        return
    end
    
    local targetPlayer = ESX.GetPlayerFromId(targetId)
    if not targetPlayer then
        TriggerClientEvent('esx:showNotification', xPlayer.source, 'Player not found!')
        return
    end
    
    if not Config.Cases[caseType] then
        TriggerClientEvent('esx:showNotification', xPlayer.source, 'Invalid case type!')
        return
    end
    
    targetPlayer.addInventoryItem(caseType, amount)
    TriggerClientEvent('esx:showNotification', xPlayer.source, 
        string.format('Gave %dx %s to %s', amount, caseType, targetPlayer.getName()))
    TriggerClientEvent('esx:showNotification', targetId, 
        string.format('You received %dx %s', amount, Config.Cases[caseType].label))
end, false, {
    help = 'Give a case to a player',
    validate = true,
    arguments = {
        {name = 'target', help = 'Target player ID', type = 'number'},
        {name = 'case', help = 'Case type', type = 'string'},
        {name = 'amount', help = 'Amount of cases', type = 'number'}
    }
})
