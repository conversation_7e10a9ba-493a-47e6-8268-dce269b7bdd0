Config = {}

-- Case configurations
Config.Cases = {
    ['weapon_case'] = {
        label = 'Weapon Case',
        price = 2500,
        items = {
            {item = 'weapon_pistol', label = 'Pistol', rarity = 'common', chance = 30},
            {item = 'weapon_smg', label = 'SMG', rarity = 'uncommon', chance = 25},
            {item = 'weapon_assaultrifle', label = 'Assault Rifle', rarity = 'rare', chance = 20},
            {item = 'weapon_sniperrifle', label = 'Sniper Rifle', rarity = 'epic', chance = 15},
            {item = 'weapon_rpg', label = 'RPG', rarity = 'legendary', chance = 8},
            {item = 'weapon_minigun', label = 'Minigun', rarity = 'mythical', chance = 2}
        }
    },
    ['skin_case'] = {
        label = 'Skin Case',
        price = 1500,
        items = {
            {item = 'skin_common', label = 'Common Skin', rarity = 'common', chance = 40},
            {item = 'skin_uncommon', label = 'Uncommon Skin', rarity = 'uncommon', chance = 30},
            {item = 'skin_rare', label = 'Rare Skin', rarity = 'rare', chance = 20},
            {item = 'skin_epic', label = 'Epic Skin', rarity = 'epic', chance = 8},
            {item = 'skin_legendary', label = 'Legendary Skin', rarity = 'legendary', chance = 2}
        }
    }
}

-- Rarity colors (CSS colors)
Config.RarityColors = {
    ['common'] = '#b0c3d9',
    ['uncommon'] = '#5e98d9',
    ['rare'] = '#4b69ff',
    ['epic'] = '#8847ff',
    ['legendary'] = '#d32ce6',
    ['mythical'] = '#eb4b4b'
}

-- Animation settings
Config.Animation = {
    duration = 5000, -- 5 seconds
    itemsToShow = 15, -- Number of items to show in the spinning animation
    slowdownStart = 0.7 -- When to start slowing down (0.7 = 70% through animation)
}

-- Key to open case menu
Config.OpenKey = 'E'
